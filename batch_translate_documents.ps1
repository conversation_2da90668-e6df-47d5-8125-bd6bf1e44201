# 脚本用于批量翻译E:\mcp-test\translated_input目录下的.docx和.md文件
# 翻译结果保存在E:\mcp-test\translated_output目录中，文件名为"原文件名+翻译.docx"

# 设置文件夹路径
$sourceFolder = "E:\mcp-test\translated_input"
$outputFolder = "E:\mcp-test\translated_output"

# 检查源文件夹是否存在
if (-not (Test-Path -Path $sourceFolder)) {
    Write-Host "错误: 文件夹 $sourceFolder 不存在!" -ForegroundColor Red
    exit
}

# 确保输出文件夹存在
if (-not (Test-Path -Path $outputFolder)) {
    New-Item -Path $outputFolder -ItemType Directory | Out-Null
    Write-Host "已创建输出文件夹: $outputFolder" -ForegroundColor Green
}

# 获取所有.docx和.md文件
$docFiles = Get-ChildItem -Path $sourceFolder -Include "*.docx", "*.md" -File

if ($docFiles.Count -eq 0) {
    Write-Host "没有找到.docx或.md文件!" -ForegroundColor Yellow
    exit
}

Write-Host "找到 $($docFiles.Count) 个文件需要翻译..." -ForegroundColor Cyan

# 创建临时Python脚本来处理文档翻译
$pythonScript = @'
import docx
from docx import Document
import sys
import os
import requests
import json
import time
import markdown
from bs4 import BeautifulSoup
import re

def translate_text(text, source_lang='zh', target_lang='en'):
    """使用智谱GLM-4-air模型API翻译文本"""
    url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer 7810b54663644292bcfd221e9ec7f0dd.aPV0rs8StKq1FmD0"
    }
    
    # 构建提示词，要求模型将中文翻译成英文
    prompt = f"请将以下中文文本翻译成英文，只返回翻译结果，不要添加任何解释或额外内容：\n\n{text}"
    
    data = {
        "model": "GLM-4-air",
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7,
        "max_tokens": 4096
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            result = response.json()
            # 从响应中提取翻译后的文本
            translated_text = result["choices"][0]["message"]["content"]
            return translated_text
        else:
            print(f"翻译请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"翻译过程中出错: {str(e)}")
        return None

def translate_docx(input_file, output_file):
    """翻译docx文件并创建中英文对照版本"""
    try:
        # 读取源文档
        doc = Document(input_file)
        
        # 创建新文档
        new_doc = Document()
        
        # 添加标题
        new_doc.add_heading('双语对照文档 / Bilingual Document', 0)
        
        # 处理每个段落
        total_paragraphs = len(doc.paragraphs)
        for i, para in enumerate(doc.paragraphs):
            # 跳过空段落
            if not para.text.strip():
                new_doc.add_paragraph('')
                continue
            
            # 获取原文
            original_text = para.text.strip()
            
            # 打印进度
            print(f"正在翻译段落 {i+1}/{total_paragraphs}")
            
            # 翻译文本
            translated_text = translate_text(original_text)
            
            if translated_text:
                # 添加原文
                chinese_para = new_doc.add_paragraph(original_text)
                chinese_para.style = 'Normal'
                
                # 添加翻译
                english_para = new_doc.add_paragraph(translated_text)
                english_para.style = 'Normal'
                
                # 添加空行分隔
                new_doc.add_paragraph('')
            else:
                # 如果翻译失败，只添加原文
                new_doc.add_paragraph(original_text)
                new_doc.add_paragraph('[翻译失败 / Translation failed]')
                new_doc.add_paragraph('')
            
            # 添加延迟以避免API限制
            time.sleep(1)
        
        # 保存新文档
        new_doc.save(output_file)
        print(f"文档已成功翻译并保存到: {output_file}")
        return True
    except Exception as e:
        print(f"处理文档时出错: {str(e)}")
        return False

def translate_markdown(input_file, output_file):
    """翻译markdown文件并创建中英文对照版本"""
    try:
        # 读取markdown文件
        with open(input_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 将markdown转换为HTML以便于解析
        html = markdown.markdown(md_content)
        soup = BeautifulSoup(html, 'html.parser')
        
        # 提取所有文本元素
        text_elements = []
        for element in soup.find_all(text=True):
            if element.strip():
                text_elements.append(element.strip())
        
        # 创建新的文档
        doc = Document()
        doc.add_heading('Markdown双语对照文档 / Bilingual Document', 0)
        
        # 处理每个文本元素
        total_elements = len(text_elements)
        for i, text in enumerate(text_elements):
            # 跳过空文本
            if not text.strip():
                continue
            
            # 打印进度
            print(f"正在翻译Markdown元素 {i+1}/{total_elements}")
            
            # 翻译文本
            translated_text = translate_text(text)
            
            if translated_text:
                # 添加原文
                chinese_para = doc.add_paragraph(text)
                chinese_para.style = 'Normal'
                
                # 添加翻译
                english_para = doc.add_paragraph(translated_text)
                english_para.style = 'Normal'
                
                # 添加空行分隔
                doc.add_paragraph('')
            else:
                # 如果翻译失败，只添加原文
                doc.add_paragraph(text)
                doc.add_paragraph('[翻译失败 / Translation failed]')
                doc.add_paragraph('')
            
            # 添加延迟以避免API限制
            time.sleep(1)
        
        # 保存为docx文件
        doc.save(output_file)
        print(f"Markdown文档已成功翻译并保存到: {output_file}")
        return True
    except Exception as e:
        print(f"处理Markdown文件时出错: {str(e)}")
        return False

def process_file(input_file, output_file):
    """根据文件类型选择适当的翻译方法"""
    file_ext = os.path.splitext(input_file)[1].lower()
    
    if file_ext == '.docx':
        return translate_docx(input_file, output_file)
    elif file_ext == '.md':
        return translate_markdown(input_file, output_file)
    else:
        print(f"不支持的文件类型: {file_ext}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python script.py 输入文件 输出文件")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        sys.exit(1)
    
    success = process_file(input_file, output_file)
    if not success:
        sys.exit(1)
'@

# 将Python脚本保存到临时文件
$tempPythonPath = [System.IO.Path]::Combine([System.IO.Path]::GetTempPath(), "translate_documents.py")
$pythonScript | Out-File -FilePath $tempPythonPath -Encoding utf8

# 安装必要的Python包
Write-Host "正在检查并安装必要的Python包..." -ForegroundColor Yellow
try {
    python -c "import markdown, bs4" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "正在安装必要的Python包..." -ForegroundColor Yellow
        python -m pip install python-docx requests markdown beautifulsoup4
    }
} catch {
    Write-Host "正在安装必要的Python包..." -ForegroundColor Yellow
    python -m pip install python-docx requests markdown beautifulsoup4
}

# 处理每个文件
foreach ($file in $docFiles) {
    $outputFileName = "$($file.BaseName)翻译.docx"
    $outputFilePath = Join-Path -Path $outputFolder -ChildPath $outputFileName
    
    Write-Host "正在翻译文件: $($file.Name) -> $outputFileName" -ForegroundColor Cyan
    
    # 执行Python脚本
    try {
        python $tempPythonPath $file.FullName $outputFilePath
        
        if (Test-Path -Path $outputFilePath) {
            Write-Host "翻译完成! 结果已保存到: $outputFilePath" -ForegroundColor Green
        } else {
            Write-Host "翻译失败，未能创建输出文件: $outputFilePath" -ForegroundColor Red
        }
    } catch {
        Write-Host "执行Python脚本时出错: $_" -ForegroundColor Red
    }
}

# 清理临时文件
Remove-Item -Path $tempPythonPath -Force

Write-Host "所有文件处理完成!" -ForegroundColor Green